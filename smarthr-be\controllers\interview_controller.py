# 1. Standard library imports
import json
import logging
from contextlib import contextmanager
from typing import List, Optional

# 2. Third-party imports
from datetime import datetime
import psycopg2
from psycopg2.extras import Json
from fastapi import HTTPException
from langchain_core.messages import HumanMessage

# 3. Internal imports
from config.config import MODELS_CONFIG
from core.config import settings
from models.enums import StatusInterview
from models.llm import inference_with_fallback, get_related_class_definitions
from models.interview import (
    InterviewCreate,
    QA_model,
    InterviewProcessingRequest,
    ExtractedAnswers,
    ParaphrasedAnswers,
    ProcessType,
    EvaluationResult,
    EvaluateInterviewNoQA,
    Interview,
    InterviewHr,
    InterviewTec
)
from models.models import SingleQuestions
from controllers.positions_controller import get_position_by_id
from controllers.candidates_controller import get_candidate_by_id

# Telemetry Section
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# Context manager for PostgreSQL cursor with error handling.
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.
    Yields:
        psycopg2.cursor: Database cursor.
    Raises:
        HTTPException: If a database error occurs.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor() as cur:
                yield cur
    except psycopg2.Error as e:
        logger.error(f"Database error occurred in get_cursor: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    finally:
        if conn:
            conn.close()


# Return a comma-separated string of interview topics in a preferred order.
def get_topics(include: str) -> str:
    """
    Return a comma-separated string of interview topics in a preferred order.
    This function takes a comma-separated string of topics to include, normalizes and filters them,
    and returns a string of topics ordered according to a predefined list. If no topics are provided,
    it returns all topics in the default order.

    Args:
        include (str): Comma-separated list of topics to include.

    Returns:
        str: Comma-separated topics string in the preferred order.
    """
    base = ""
    desired_order = ['Technical Skills', 'Methodologies', 'Soft Skills', 'Language - Tools']

    # Map lowercase to original case
    lower_to_original = {item.lower(): item for item in desired_order}

    # Normalize input items to lowercase
    input_items = [item.strip().lower() for item in include.split(",") if item.strip()]

    if not input_items:
        return f"{base}{', '.join(desired_order)}."

    # Keep the order from desired_order and match only those present
    ordered = [lower_to_original[item.lower()] for item in desired_order if item.lower() in input_items]

    return f"{base}{', '.join(ordered)}."


# Process an interview transcript to extract or paraphrase answers using an LLM.
def process_interview(request: InterviewProcessingRequest):
    """
    Process an interview transcript to extract or paraphrase answers using an LLM.

    Parameters
    ----------
    request : InterviewProcessingRequest
        The interview processing request containing questions, transcript, and process type.

    Returns
    -------
    Any
        The extracted or paraphrased answers as defined by the schema.
    Raises
    ------
    RuntimeError
        If all LLM providers fail to return a result.
    """
    if request.process_type == ProcessType.EXTRACT:
        answer_schema = ExtractedAnswers
        prompt = (
            "Extract direct answers from the transcript for each question. "
            "Return ONLY the candidate's responses matching each question in order."
        )
    else:
        answer_schema = ParaphrasedAnswers
        prompt = (
            "Paraphrase the candidate's answers using the full context of the transcript, ensuring that:\n"
            "- The paraphrased answer remains faithful to what was actually said.\n"
            "- If relevant details appear in other parts of the transcript, include a 'complement_from' field.\n"
            "- Do NOT introduce new information or modify qualifications.\n"
            "- Return JSON following the provided schema."
        )

    user_message = HumanMessage(
        content=(
            "Questions:\n"
            + "\n".join(f"{idx + 1}. {question}" for idx, question in enumerate(request.questions))
            + "\n\nTranscript:\n"
            + request.transcript
        )
    )

    schema_text = get_related_class_definitions(answer_schema)
    result = inference_with_fallback(
        task_prompt=prompt,
        model_schema=answer_schema,
        user_messages=[user_message],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("All LLM providers failed to return a result.")
    return result


# Process an interview using an LLM and persist the extracted or paraphrased answers.
def run_and_persist_interview(interview_id: str, process_type: ProcessType):
    """
    Process an interview using an LLM and persist the extracted or paraphrased answers.

    Args:
        interview_id (str): The unique identifier of the interview.
        process_type (ProcessType): The type of processing to perform (EXTRACT or PARAPHRASE).

    Returns:
        Any: The processed answers as defined by the schema, or None if the interview or questionnaire is not found.
    """
    # Fetch questionnaire and transcript for the given interview
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()

    if not row:
        # Interview or questionnaire not found
        return None

    questionnaire_data, transcript = row
    questions = [q["question"] for q in questionnaire_data.get("questions", [])]

    # Prepare the processing request
    processing_request = InterviewProcessingRequest(
        questions=questions,
        transcript=transcript,
        process_type=process_type
    )

    # Process the interview using the LLM
    processed_result = process_interview(processing_request)

    # Persist the processed answers in the database
    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET anwers_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(processed_result.model_dump()), interview_id)
        )

    return processed_result


# Evaluate an interview without a question/answer set, using only transcript, candidate, and position info.
def evaluate_interview_with_no_qa(interview_id: str) -> EvaluationResult:
    """
    Evaluate an interview without a question/answer set, using only transcript, candidate, and position info.

    Parameters
    ----------
    interview_id : str
        The unique identifier of the interview.

    Returns
    -------
    EvaluationResult
        The evaluation result as returned by the LLM.
    Raises
    ------
    HTTPException
        If the interview is not found or LLM evaluation fails.
    """
    try:
        # Retrieve position info, candidate info, and transcript for the interview
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT p.position_info, c.candidate_info, i.transcript_hr
                FROM interviews i
                JOIN positions_smarthr p ON i.position_id = p.id
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id = %s;
                """,
                (interview_id,)
            )
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Interview not found (evaluate_interview_with_no_qa)")
        position_info, candidate_info, transcript_hr = row

        # Prepare prompt and schema for LLM evaluation
        task_prompt = (
            "Evaluate the interview transcript, candidate info, and position info.\n"
            "Return JSON that matches the provided schema.\n"
            "IMPORTANT RULES:\n"
            "• Provide an overall_seniority (senior|mid|junior) based on the transcript and candidate info."
        )
        schema_text = get_related_class_definitions(EvaluateInterviewNoQA)

        llm_input = {
            "position_info": position_info,
            "candidate_info": candidate_info,
            "transcript": transcript_hr
        }

        result = inference_with_fallback(
            task_prompt=task_prompt,
            model_schema=EvaluateInterviewNoQA,
            user_messages=[HumanMessage(content=json.dumps(llm_input, ensure_ascii=False))],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )

        if not result:
            raise RuntimeError("LLM evaluation failed")

        # Persist the evaluation result in the database
        with get_cursor() as cur:
            cur.execute(
                """
                UPDATE interviews
                SET interview_data = %s,
                    updated_at = NOW()
                WHERE id = %s;
                """,
                (Json(result.model_dump()), interview_id)
            )
        return result

    except Exception as exc:
        # Log the error and raise an HTTPException
        logger.error(f"Error occurred while evaluating interview without QA: {str(exc)}")
        raise HTTPException(
            status_code=404,
            detail=f"Interview not found (except: evaluate_interview_with_no_qa): {str(exc)}"
        )


# Evaluate an interview by comparing candidate answers with expected answers.
def evaluate_interview(interview_id: str) -> EvaluationResult:
    """
    Evaluate an interview by comparing candidate answers with expected answers.

    Parameters
    ----------
    interview_id : str
        The unique identifier of the interview.

    Returns
    -------
    EvaluationResult
        The evaluation result as returned by the LLM.
    Raises
    ------
    HTTPException
        If the interview is not found or LLM evaluation fails.
    """
    # Fetch expected questions, actual answers, feedback, and transcript for the interview
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.anwers_data, i.feedback_tec, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()

    # If no questionnaire or answers found, fallback to evaluation without QA
    if not row:
        return evaluate_interview_with_no_qa(interview_id)

    expected_data, actual_answers, feedback_tec, transcript_tec = row

    # If expected questions are missing or empty, fallback to evaluation without QA
    if expected_data is None or not expected_data.get('questions'):
        return evaluate_interview_with_no_qa(interview_id)
    # elif expected is not None and expected.get('questions'):
    #     run_and_persist_interview(interview_id, ProcessType.EXTRACT)
    ## End first validation

    # Prepare prompt and schema for LLM evaluation
    task_prompt = (
    "Evaluate the candidate's responses against expected senior/mid/junior levels by integrating all available information. "
    "For each question, identify 'detected_seniority' (senior|mid|junior) and provide an explanation. "
    "Additionally, determine the 'overall_seniority' and calculate the 'percentage_of_match'. "
    "Deliver a JSON output conforming to the specified schema."
    "CRITICAL INTEGRATION REQUIREMENTS:\n"
    "• Utilize the transcript_tec to understand the conversation flow and the candidate's reasoning.\n"
    "• Cross-reference responses with feedback comments to uncover patterns and insights.\n"
    "• Link specific strengths/weaknesses noted in feedback_comments to supporting evidence in the Q&A.\n"
    "• Integrate feedback_comments explicitly in the explanation field of the schema.\n"
    "• Conclude with an 'overall_seniority' based on a holistic analysis of all data sources, excluding comments from evaluation. Incorporate comments from the transcript_excerpt to improve evaluation accuracy.\n"
    "• Provide a detailed explanation of how comments, transcript, and answers were integrated.\n"
    "• Ensure the output is a valid JSON object with the correct structure and syntax.\n"
    "• Ensure comments are included in the transcription from the transcript_excerpt to enhance the evaluation process and provide necessary context.\n"
)
    schema_text = get_related_class_definitions(EvaluationResult)

    # Prepare comprehensive context for LLM
    evaluation_context = {
        'expected': expected_data,
        'actual': actual_answers
    }

    # Add feedback comments if available
    if feedback_tec:
        evaluation_context['feedback_comments'] = feedback_tec

    # Add transcript excerpt if available (truncate if too long)
    if transcript_tec:
        transcript_excerpt = transcript_tec[:1000] + "..." if len(transcript_tec) > 1000 else transcript_tec
        evaluation_context['transcript_excerpt'] = transcript_excerpt

    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=EvaluationResult,
        user_messages=[HumanMessage(content=json.dumps(evaluation_context, ensure_ascii=False))],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )

    if not result:
        raise RuntimeError("LLM evaluation failed")

    # Persist the evaluation result in the database
    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET interview_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )

    return result


# Generate and persist interview questions for a given position.
def generate_and_persist_qa(position_id: str, n_questions: int, include: str, current_user: str) -> QA_model:
    """
    Generate and persist interview questions for a given position.

    Parameters
    ----------
    position_id : str
        The unique identifier of the position.
    n_questions : int
        The number of questions to generate.
    include : str
        Comma-separated list of topics to include in the questions.
    current_user : str
        The user performing the operation.

    Returns
    -------
    QA_model
        The generated QA model containing the questions.
    Raises
    ------
    HTTPException
        If the position is not found or regeneration is not allowed.
    RuntimeError
        If the LLM fails to generate the questionnaire.
    """
    # Fetch position information
    position = get_position_by_id(position_id)
    if not position:
        raise HTTPException(status_code=404, detail="Position not found")

    # Check if questions already exist and if regeneration is allowed
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT data, allow_regeneration FROM interview_questions WHERE position_id = %s;
            """,
            (position_id,)
        )
        row = cur.fetchone()
        allow_regeneration = True if not row or row[1] is None else row[1]
        if not allow_regeneration:
            raise HTTPException(
                status_code=400,
                detail=f"Interview questions already exist for position {position_id} and regeneration is not allowed"
            )

    # Adjust the field names if your JSON differs.
    info: dict = position.position_info or {}
    full_description = json.dumps(info, ensure_ascii=False)

    # 2) Build prompt
    topics_text = get_topics(include)
    # print("topics_text", topics_text)
    task_prompt = f"""
        You are tasked with creating a structured interview questionnaire designed to evaluate **technical and methodological skills** while clearly differentiating levels of seniority among candidates for a specific role.

        Role Description:
        {full_description}

        **Please generate exactly {n_questions} questions based on the following topics: {topics_text}. For each question, ensure the output includes:**

        1. A sequential question_number ranging from 1 to {n_questions}.
        2. A single tag indicating the specific topic addressed, selected exclusively from: {topics_text}.
        3. Three distinct answers that reflect different seniority levels:
        - junior_answer
        - mid_answer
        - senior_answer

        **Guidelines for Answer Construction (Chain of Thought per level):**

        - senior_answer: Highlight advanced concepts, frameworks, and strategies. Emphasize decision-making, scalability, efficiency, and alignment with business value. Conclude with measurable outcomes or impact on organizational objectives.  
        - mid_answer: Describe practical execution, tools, and methodologies in detail. Show structured problem-solving and collaboration. Conclude with how these practices improve workflows or contribute to project/team success.  
        - junior_answer: Cover foundational concepts, learning in practice, and hands-on skills. Emphasize adaptability, eagerness to learn, and contribution to immediate team objectives.  

        **Formatting Rules:**
        - Deliver the output strictly in JSON format with valid syntax.  
        - Each topic from {topics_text} must appear in at least one question.  
        - Each question must have exactly one tag.  
        - Do not combine tags (e.g., "SOFT SKILLS METHODOLOGIES" is prohibited).  
        - Ensure clear differentiation between junior, mid, and senior answers — avoid repetition or generic filler.  
        - Avoid referencing seniority explicitly (e.g., "As a junior…" or "With X years of experience").  
        - Keep answers professional, substantive, and business-relevant.

        **Example (Agile Methodologies — Sprint Planning):**  
        - Junior: Basic understanding of Agile/Scrum, learning task organization, showing how participation supports team collaboration.  
        - Mid: Refining backlog, coordinating with stakeholders, ensuring adaptability and efficiency in delivery.  
        - Senior: Driving strategic alignment, leading planning sessions, ensuring measurable improvements in delivery and business outcomes.  
        """
    
    schema_text = get_related_class_definitions(QA_model)
    # print("task_prompt", task_prompt)
    qa_model = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=QA_model,
        user_messages=[HumanMessage(content="")],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not qa_model:
        raise RuntimeError("LLM failed to generate questionnaire")

    # Persist the generated questions in the database
    with get_cursor() as cur:
        cur.execute(
            """
            INSERT INTO interview_questions 
                (position_id, data, created_by, created_at, updated_by, updated_at) 
            VALUES 
                (%s, %s, %s, NOW(), %s, NOW())
            ON CONFLICT (position_id) DO UPDATE
            SET 
                data = EXCLUDED.data,
                updated_by = EXCLUDED.updated_by,
                updated_at = NOW();
            """,
            (position_id, Json(qa_model.model_dump()), current_user, current_user)
        )
    return qa_model


# Create interviews for a given position and a list of candidates.
def create_interviews_for_position(position_id: str, interview_creations: List[InterviewCreate]) -> List[Interview]:
    """
    Create interviews for a given position and a list of candidates.
    Parameters
    ----------
    position_id : str
        The unique identifier of the position.
    interview_creations : List[InterviewCreate]
        List of interview creation data, each containing candidate_id and optional analysis_data.
    Returns
    -------
    List[Interview]
        List of created interviews for the given position.
    Raises
    ------
    HTTPException
        If a database error occurs or invalid candidate data is provided.
    """
    try:
        candidate_ids = [item.candidate_id for item in interview_creations]
        logger.debug(f"Creating interviews for position_id: {position_id} and candidate_ids: {candidate_ids}")

        with get_cursor() as cur:
            for interview_data in interview_creations:
                candidate_id = interview_data.candidate_id
                logger.debug(f"Processing candidate_id: {candidate_id} for position_id: {position_id}")

                if not candidate_id or not isinstance(candidate_id, str) or len(candidate_id) != 36:
                    logger.warning(f"Invalid candidate_id format: {candidate_id}")
                    continue

                # Check if candidate exists
                if not get_candidate_by_id(candidate_id):
                    logger.warning(f"Candidate does not exist: {candidate_id}")
                    continue

                # Check if interview already exists for this candidate and position
                if fetch_interview_by_position_id_candidate_id(position_id, candidate_id):
                    logger.info(f"Interview already exists for candidate_id: {candidate_id}, position_id: {position_id}")
                    continue

                # Insert new interview
                cur.execute(
                    """
                    INSERT INTO interviews 
                        (position_id, candidate_id, analysis_data, status_hr, status_tec, created_at, updated_at)
                    VALUES 
                        (%s, %s, %s, 'not_scheduled', 'not_scheduled', NOW(), NOW())
                    RETURNING id, position_id, candidate_id
                    """,
                    (
                        position_id,
                        candidate_id,
                        Json(interview_data.analysis_data if interview_data.analysis_data else {}),
                    ),
                )

        return fetch_all_interviews_by_position_id(position_id)
    except psycopg2.Error as e:
        logger.error(f"Database error occurred while creating interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"create_interviews_for_position: Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred while creating interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error occurred while creating interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Fetch all interviews for a given position ID.
def fetch_all_interviews_by_position_id(position_id: str) -> List[Interview]:
    """
    Fetch all interviews for a given position ID.
    Parameters
    ----------
    position_id : str
        The unique identifier of the position.
    Returns
    -------
    List[Interview]
        A list of Interview objects associated with the given position.
    Raises
    ------
    HTTPException
        If a database error occurs.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                       i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                       i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                       i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text = %s AND c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id,),
            )
            rows = cur.fetchall()

        interviews: List[Interview] = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        logger.error(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_all_interviews_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Update interview feedback for the HR team.
def update_interview_hr(interview_data: InterviewHr) -> Interview:
    """
    Update interview feedback for the HR team.
    Parameters
    ----------
    interview_data : InterviewHr
        Interview HR feedback data.
    Returns
    -------
    Interview
        The updated interview object.
    Raises
    ------
    HTTPException
        If the interview is not found or already completed/cancelled.
    """
    # Fetch the interview to ensure it exists and is not completed/cancelled
    interview = fetch_interview_by_position_id_candidate_id(
        interview_data.position_id, interview_data.candidate_id
    )
    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found")

    if interview.status_hr in (
        StatusInterview.COMPLETED.value,
        StatusInterview.CANCELLED.value,
    ):
        raise HTTPException(
            status_code=400, detail="Interview already completed or cancelled"
        )

    update_query = """
        UPDATE interviews SET
            feedback_hr = %s,
            recruiter_hr_id = %s,
            scheduled_hr_id = %s,
            interview_date_hr = %s,
            feedback_date_hr = %s,
            status_hr = %s,
            recommendation_hr = %s,
            transcript_hr = %s,
            updated_at = NOW()
        WHERE position_id = %s AND candidate_id = %s
        RETURNING id, position_id, candidate_id, feedback_hr, interview_date_hr, feedback_date_hr, status_hr,
                  recommendation_hr, transcript_hr, feedback_tec, interview_date_tec, feedback_date_tec, status_tec,
                  recommendation_tec, transcript_tec, created_at, updated_at, recruiter_hr_id, scheduled_hr_id,
                  recruiter_tec_id, scheduled_tec_id, analysis_data
    """
    params = [
        Json(interview_data.feedback_hr),
        interview_data.recruiter_hr_id,
        interview_data.scheduled_hr_id,
        interview_data.interview_date_hr,
        interview_data.feedback_date_hr,
        interview_data.status_hr,
        interview_data.recommendation_hr,
        interview_data.transcript_hr,
        interview_data.position_id,
        interview_data.candidate_id,
    ]

    with get_cursor() as cur:
        cur.execute(update_query, params)
        row = cur.fetchone()

    if not row:
        return None

    return Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21],
    )


# Update interview feedback for the technical team.
def update_interview_tec(interview_data: InterviewTec) -> Interview:
    """
    Update interview feedback for the technical team.
    Parameters
    ----------
    interview_data : InterviewTec
        Interview technical feedback data.
    Returns
    -------
    Interview
        The updated interview object.
    Raises
    ------
    HTTPException
        If the interview is not found or already completed/cancelled.
    """
    # Fetch the interview to ensure it exists and is not completed/cancelled
    interview = fetch_interview_by_position_id_candidate_id(
        interview_data.position_id, interview_data.candidate_id
    )
    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found (update_interview_tec)")

    if interview.status_tec in (
        StatusInterview.COMPLETED.value,
        StatusInterview.CANCELLED.value,
    ):
        raise HTTPException(
            status_code=400, detail="Interview already completed or cancelled"
        )

    update_query = """
        UPDATE interviews SET
            feedback_tec = %s,
            recruiter_tec_id = %s,
            scheduled_tec_id = %s,
            interview_date_tec = %s,
            feedback_date_tec = %s,
            status_tec = %s,
            recommendation_tec = %s,
            transcript_tec = %s,
            updated_at = NOW()
        WHERE position_id = %s AND candidate_id = %s
        RETURNING id, position_id, candidate_id, feedback_hr, interview_date_hr, feedback_date_hr, status_hr,
                  recommendation_hr, transcript_hr, feedback_tec, interview_date_tec, feedback_date_tec, status_tec,
                  recommendation_tec, transcript_tec, created_at, updated_at, recruiter_hr_id, scheduled_hr_id,
                  recruiter_tec_id, scheduled_tec_id, analysis_data
    """
    params = [
        Json(interview_data.feedback_tec),
        interview_data.recruiter_tec_id,
        interview_data.scheduled_tec_id,
        interview_data.interview_date_tec,
        interview_data.feedback_date_tec,
        interview_data.status_tec,
        interview_data.recommendation_tec,
        interview_data.transcript_tec,
        interview_data.position_id,
        interview_data.candidate_id,
    ]

    with get_cursor() as cur:
        cur.execute(update_query, params)
        row = cur.fetchone()

    if not row:
        return None

    updated_interview = Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21],
    )

    # If the technical interview is completed, process and evaluate it
    if updated_interview.status_tec == StatusInterview.COMPLETED.value:
        run_and_persist_interview(updated_interview.id, ProcessType.EXTRACT)
        evaluate_interview(updated_interview.id)

    return updated_interview


# Fetch a single interview by position ID and candidate ID.
def fetch_interview_by_position_id_candidate_id(position_id: str, candidate_id: str) -> Optional[Interview]:
    """
    Fetch a single interview by position ID and candidate ID.
    Parameters
    ----------
    position_id : str
        The unique identifier of the position.
    candidate_id : str
        The unique identifier of the candidate.
    Returns
    -------
    Optional[Interview]
        The Interview object if found, otherwise None.
    Raises
    ------
    HTTPException
        If a database error occurs.
    """
    try:
        logger.debug(f"Fetching interview for position_id: {position_id}, candidate_id: {candidate_id}")
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                       i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                       i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info, 
                       i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text = %s AND i.candidate_id::text = %s AND c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id, candidate_id),
            )
            row = cur.fetchone()
        if not row:
            return None
        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        logger.error(f"Database error occurred while fetching interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_position_id_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred while fetching interview: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred while fetching interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Fetch interview questions by position ID
def fetch_questions_by_position_id(position_id: str) -> SingleQuestions:
    """
    Retrieve the interview questions for a given position ID.
    Parameters
    ----------
    position_id : str
        The unique identifier of the position.
    Returns
    -------
    SingleQuestions
        The questions object associated with the specified position.
    Raises
    ------
    HTTPException
        If questions are not found or a database error occurs.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT id, position_id, data, created_at, updated_at, allow_regeneration, created_by, updated_by
                FROM interview_questions
                WHERE position_id::text = %s
                """,
                (position_id,),
            )
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Questions not found")
        return SingleQuestions(
            id=str(row[0]),
            position_id=str(row[1]),
            data=row[2],
            created_at=row[3],
            updated_at=row[4],
            allow_regeneration=row[5],
            created_by=row[6],
            updated_by=row[7]
        )
    except psycopg2.Error as e:
        logger.error(f"Database error occurred while fetching questions by position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_questions_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred while fetching questions by position_id: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred while fetching questions by position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Delete an interview for the given position and candidate IDs
def delete_interview(position_id: str, candidate_id: str) -> bool:
    """
    Delete an interview for the given position and candidate IDs.
    Parameters
    ----------
    position_id : str
        The unique identifier of the position.
    candidate_id : str
        The unique identifier of the candidate.
    Returns
    -------
    bool
        True if the interview was deleted successfully.
    Raises
    ------
    HTTPException
        If the interview is not found, is in progress or completed, or a database error occurs.
    """
    try:
        # Fetch the interview to check its status
        interview = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
        if not interview:
            raise HTTPException(status_code=404, detail="Interview not found")

        # Prevent deletion if the technical interview is in progress or completed
        if interview.status_tec in (
            StatusInterview.IN_PROGRESS.value,
            StatusInterview.COMPLETED.value,
        ):
            raise HTTPException(
                status_code=400,
                detail="Interview already completed or in progress"
            )

        with get_cursor() as cur:
            cur.execute(
                """
                DELETE FROM interviews WHERE id = %s
                """,
                (interview.id,),
            )
        return True
    except psycopg2.Error as e:
        logger.error(f"Database error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"delete_interview. Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Fetch all interviews for a given candidate ID
def fetch_interviews_by_candidate_id(candidate_id: str) -> List[Interview]:
    """
    Retrieve all interviews associated with a specific candidate ID.
    Parameters
    ----------
    candidate_id : str
        The unique identifier of the candidate.
    Returns
    -------
    List[Interview]
        A list of Interview objects for the given candidate.
    Raises
    ------
    HTTPException
        If a database error occurs.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                       i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                       i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                       i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.candidate_id::text = %s AND c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (candidate_id,),
            )
            rows = cur.fetchall()

        interviews: List[Interview] = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        logger.error(f"Database error occurred while fetching interviews by candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interviews_by_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException occurred while fetching interviews by candidate_id: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred while fetching interviews by candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Fetch a single interview by its unique interview ID
def fetch_interview_by_interview_id(interview_id: str) -> Optional[Interview]:
    """
    Retrieve a single interview by its unique interview ID.

    Parameters
    ----------
    interview_id : str
        The unique identifier of the interview.

    Returns
    -------
    Optional[Interview]
        The Interview object if found, otherwise None.

    Raises
    ------
    HTTPException
        If a database error occurs.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                       i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                       i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                       i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id::text = %s AND c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (interview_id,),
            )
            row = cur.fetchone()
        if not row:
            return None

        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None) if row[19] else None,
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        logger.error(f"Database error occurred while fetching interview by interview_id: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"fetch_interview_by_interview_id. Database error occurred: {str(e)}"
        )
    except HTTPException as e:
        logger.error(f"HTTPException occurred while fetching interview by interview_id: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error occurred while fetching interview by interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Re-evaluate an interview by its ID
def re_evaluate_interview(interview_id: str) -> Interview:
    """
    Re-evaluate an interview by its ID.
    Parameters
    ----------
    interview_id : str
        The unique identifier of the interview.
    Returns
    -------
    Interview
        The updated Interview object after re-evaluation.
    Raises
    ------
    HTTPException
        If the interview is not found or the technical transcript is missing.
    """
    # Fetch the interview by ID
    interview = fetch_interview_by_interview_id(interview_id)
    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found (re_evaluate_interview)")

    if not interview.transcript_tec:
        raise HTTPException(status_code=400, detail="Technical transcript not found")

    # If answers data is missing, process and persist the interview
    if not interview.anwers_data:
        run_and_persist_interview(interview.id, ProcessType.EXTRACT)

    # Evaluate the interview and persist the results
    evaluate_interview(interview.id)

    # Fetch and return the updated interview
    updated_interview = fetch_interview_by_interview_id(interview.id)
    return updated_interview


# Update the allow_regeneration status for a specific question
def update_question_regeneration_status(
    position_id: str, question_id: str, allow_regeneration: bool
) -> bool:
    """
    Update the allow_regeneration status for a specific question in the interview_questions table.

    Parameters
    ----------
    position_id : str
        The unique identifier of the position to which the question belongs.
    question_id : str
        The unique identifier of the question to update.
    allow_regeneration : bool
        Flag indicating whether regeneration is allowed for this question.

    Returns
    -------
    bool
        True if the update was successful.

    Raises
    ------
    HTTPException
        If the question is not found or a database error occurs.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                UPDATE interview_questions
                SET allow_regeneration = %s,
                    updated_at = NOW()
                WHERE id = %s AND position_id = %s;
                """,
                (allow_regeneration, question_id, position_id)
            )
            if cur.rowcount == 0:
                raise HTTPException(status_code=404, detail="Question not found")
        return True
    except psycopg2.Error as e:
        logger.error(f"Database error occurred while updating question regeneration status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"update_question_regeneration_status. Database error occurred: {str(e)}"
        )
    except HTTPException as e:
        logger.error(f"HTTPException occurred while updating question regeneration status: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error occurred while updating question regeneration status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
